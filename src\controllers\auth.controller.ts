import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { StatusCodes } from 'http-status-codes';
import User from '../models/User';
import { sendOtpEmail } from './helpers/OTP';

const JWT_SECRET = process.env.JWT_SECRET;
if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined in the environment variables');
}


export const signup = async (req: Request, res: Response) => {
  try {
    const { email, fullname, phoneNumber, password, confirmPassword, role } = req.body;

    if(!email){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Email is required' });
    }
    if(!fullname){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Full name is required' });
    }
    if(!phoneNumber){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Phone number is required' });
    }
    if(!password){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Password is required' });
    }
    if(!confirmPassword){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Confirm password is required' });
    }
    if(!role){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Role is required' });
    }

    if(password !== confirmPassword){
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }

    if(role !== 'user' && role !== 'businessOwner'){
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invalid role' });
    }

    // Check if user already exists
    let user = await User.findOne({ email });
    if (user) {
      return res.status(StatusCodes.CONFLICT).json({ message: 'User already exists' });
    }

      const newUser = new User({
        email: email.toLowerCase(),
        fullname,
        phoneNumber,
        password,
        role,
        isEmailVerified: false,
        isBusinessOwnerVerfied: false
      });

      await newUser.save();

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // Send OTP to user's email
    await sendOtpEmail(email, otp, 'signup');

    // Set OTP value to null before storing it (ensures old value is cleared)
    newUser['otp'] = "";  
    newUser['otpCreatedAt'] = new Date();   // Store the current time as OTP generation time
    newUser['otp'] = otp;   // Store the new OTP
    await newUser.save();

    return res.status(StatusCodes.OK).json({ message: 'User created. OTP sent to your email',
        user: {
        id: newUser._id,
        fullname: newUser.fullname,
        email: newUser.email,
        isEmailVerified: newUser.isEmailVerified,
        role: newUser.role
      }
     });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error sending OTP', error });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const { email, password, role } = req.body;

    if(!email || !password){
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'All fields are required' });
    }
    if(role !== 'user' && role !== 'businessOwner'){
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invalid role' });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }

    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid credentials' });
    }

    if (user.role !== role) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid role. you are not authorized to access this resource' });
    }

    if (!user.isEmailVerified) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Email not verified' });
    }

    // Generate JWT token
    const payload = { _id: user._id, role: user.role}
    const accessToken = jwt.sign(payload, JWT_SECRET,
    { expiresIn: '30d' });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Login successful',
      user: {
        id: user._id,
        fullname: user.fullname,
        email: user.email,
        isEmailVerified: user.isEmailVerified,
        role: user.role
      },
      accessToken
    });
} catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ 
        success: false,
        message: 'Error logging in', error });
  }

};

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email, password, confirmPassword } = req.body;
    if(!email){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Email is required' });
    }
    if(!password){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'New password is required' });
    }
    if(!confirmPassword){
        return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Confirm password is required' });
    }
    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }
    const isMatch = await user.comparePassword(password);
    if (isMatch) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'New password cannot be the same as the old password' });
    }
    if(password !== confirmPassword){
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }
    user.password = password;
    await user.save();
    return res.status(StatusCodes.OK).json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error changing password', error });
  }
};