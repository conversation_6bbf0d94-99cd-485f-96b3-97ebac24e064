{"name": "oyrq-backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts"}, "repository": {"type": "git", "url": "git+https://github.com/TechExpert1/oyrq-backend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/TechExpert1/oyrq-backend/issues"}, "homepage": "https://github.com/TechExpert1/oyrq-backend#readme", "description": "", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/body-parser": "^1.19.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-rate-limit": "^5.1.3", "@types/helmet": "^0.0.48", "@types/http-status-codes": "^1.2.0", "@types/jsonwebtoken": "^9.0.10", "@types/mongoose": "^5.11.96", "@types/node": "^24.2.0", "@types/nodemailer": "^6.4.17", "bcrypt": "^6.0.0", "ts-node": "^10.9.2"}, "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "http": "^0.0.1-security", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.1", "nodemailer": "^7.0.5"}}